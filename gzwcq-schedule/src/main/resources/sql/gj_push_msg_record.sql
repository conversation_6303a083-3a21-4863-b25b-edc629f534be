-- 预警推送消息记录表
-- 用于实现模型1和模型2的去重逻辑
-- <AUTHOR>

DROP TABLE IF EXISTS `gj_push_msg_record`;
CREATE TABLE `gj_push_msg_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `warn_model` tinyint(1) NOT NULL COMMENT '预警模型：1-模型1，2-模型2',
  `enterprise_name` varchar(255) NOT NULL COMMENT '企业名称',
  `enterprise_code` varchar(100) DEFAULT NULL COMMENT '企业组织机构代码',
  `jbxx_id` varchar(32) DEFAULT NULL COMMENT '基本信息ID（模型2使用）',
  `rg_timemark` datetime DEFAULT NULL COMMENT '登记时间轮（模型2使用）',
  `push_time` datetime NOT NULL COMMENT '推送时间',
  `push_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '推送状态：1-成功，0-失败',
  `push_response` text COMMENT '推送响应信息',
  `unique_key` varchar(100) NOT NULL COMMENT '推送唯一标识',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) DEFAULT 'system' COMMENT '创建人',
  `last_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `last_update_user` varchar(50) DEFAULT 'system' COMMENT '最后更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unique_key` (`unique_key`),
  KEY `idx_warn_model_enterprise` (`warn_model`, `enterprise_name`),
  KEY `idx_warn_model_jbxx_rg` (`warn_model`, `jbxx_id`, `rg_timemark`),
  KEY `idx_push_time` (`push_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送消息记录表';

-- 创建索引说明：
-- 1. uk_unique_key: 确保推送唯一标识不重复
-- 2. idx_warn_model_enterprise: 支持模型1按企业名称去重查询
-- 3. idx_warn_model_jbxx_rg: 支持模型2按登记信息去重查询
-- 4. idx_push_time: 支持按推送时间查询
